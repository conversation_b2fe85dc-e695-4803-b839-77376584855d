/**
 * SSM Parameter Store API
 * A RESTful API for managing AWS Systems Manager Parameter Store parameters
 */

require("dotenv").config();
const fs = require("fs");
const path = require("path");
const express = require("express");
const {
  SSMClient,
  GetParametersByPathCommand,
  PutParameterCommand,
} = require("@aws-sdk/client-ssm");

// Initialize Express app with JSON middleware
const app = express();
app.use(express.json());

// AWS Configuration
const region = process.env.AWS_REGION || "eu-west-1";
const PATH_PREFIX = process.env.SSM_PATH_PREFIX || "/";
const PORT = process.env.PORT || 3000;

// Initialize AWS SSM Client
const ssmClient = new SSMClient({
  region,
  // Credentials loaded from: environment variables, AWS CLI, or IAM roles
});

/**
 * Retrieves all parameters from AWS Parameter Store within the configured path prefix
 * @returns {Promise<Array>} Array of parameter objects
 */
async function getParameters() {
  console.log(`Fetching parameters with path prefix: ${PATH_PREFIX}`);
  console.log(`Using AWS region: ${region}`);

  let nextToken = null;
  let allParameters = [];

  do {
    const command = new GetParametersByPathCommand({
      Path: PATH_PREFIX,
      Recursive: true,
      WithDecryption: true,
      NextToken: nextToken,
      MaxResults: 10,
    });

    const response = await ssmClient.send(command);

    if (response.Parameters) {
      allParameters = [...allParameters, ...response.Parameters];
    }

    nextToken = response.NextToken;
  } while (nextToken);

  console.log(`Found ${allParameters.length} parameters`);
  return allParameters;
}

/**
 * Sets or updates a parameter in AWS Parameter Store
 * @param {string} name - Parameter name/path
 * @param {string} value - Parameter value
 * @param {string} type - Parameter type (String, StringList, SecureString)
 * @param {string} description - Parameter description
 * @param {boolean} overwrite - Whether to overwrite existing parameter
 * @returns {Promise<Object>} Success response with parameter details
 */
async function setParameter(name, value, type = "String", description = "", overwrite = true) {
  console.log(`Setting parameter: ${name}`);

  const command = new PutParameterCommand({
    Name: name,
    Value: value,
    Type: type,
    Description: description,
    Overwrite: overwrite,
  });

  const response = await ssmClient.send(command);
  console.log(`✅ Parameter ${name} set successfully`);

  return {
    success: true,
    name: name,
    version: response.Version,
    message: `Parameter ${name} set successfully`,
  };
}

/**
 * Generates markdown content for parameters
 * @param {Array} parameters - Array of parameter objects
 * @returns {string} Markdown formatted content
 */
function generateMarkdownReport(parameters) {
  const tableHeader = "| Parameter Name | Type | Value |\n|---------------|------|-------|\n";
  
  const tableRows = parameters
    .map((p) => {
      const displayValue = p.Type === "SecureString" 
        ? "[SECURE VALUE]"
        : p.Value?.length > 30
        ? p.Value.substring(0, 30) + "..."
        : p.Value;

      return `| ${p.Name} | ${p.Type} | ${displayValue} |`;
    })
    .join("\n");

  return `# SSM Parameters Report

**Generated:** ${new Date().toLocaleString()}
**Region:** ${region}
**Path Prefix:** ${PATH_PREFIX}
**Total Parameters:** ${parameters.length}

## Parameters

${tableHeader}${tableRows}

---
*Report generated by SSM Parameter Store API*
`;
}

/**
 * Saves markdown report to files
 * @param {string} content - Markdown content to save
 */
function saveMarkdownReport(content) {
  const outputDir = "ssm-data";
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
  const outputFile = path.join(outputDir, `ssm-parameters-${timestamp}.md`);
  const latestFile = path.join(outputDir, "ssm-parameters-latest.md");

  fs.writeFileSync(outputFile, content);
  fs.writeFileSync(latestFile, content);
}

// API Routes

/**
 * GET /parameters - Retrieve parameters
 * Query params: format=json|markdown
 */
app.get("/parameters", async (req, res) => {
  try {
    const allParameters = await getParameters();
    const format = req.query.format || "json";

    if (format === "markdown") {
      const markdownContent = generateMarkdownReport(allParameters);
      saveMarkdownReport(markdownContent);
      
      res.setHeader("Content-Type", "text/markdown");
      res.send(markdownContent);
    } else {
      res.json({
        success: true,
        region: region,
        pathPrefix: PATH_PREFIX,
        totalParameters: allParameters.length,
        parameters: allParameters.map((p) => ({
          name: p.Name,
          type: p.Type,
          value: p.Type === "SecureString" ? "[SECURE VALUE]" : p.Value,
          version: p.Version,
          lastModifiedDate: p.LastModifiedDate,
          arn: p.ARN,
        })),
      });
    }
  } catch (error) {
    console.error("Error retrieving parameters:", error.message);
    res.status(500).json({
      success: false,
      error: error.name || "RetrievalError",
      message: "Failed to retrieve parameters",
    });
  }
});

/**
 * PUT /parameters - Set or update a parameter
 * Body: { name, value, type?, description?, overwrite? }
 */
app.put("/parameters", async (req, res) => {
  try {
    const { name, value, type, description, overwrite } = req.body;

    // Validate required fields
    if (!name || value === undefined) {
      return res.status(400).json({
        success: false,
        error: "ValidationError",
        message: "Both 'name' and 'value' are required",
      });
    }

    // Validate parameter type
    const validTypes = ["String", "StringList", "SecureString"];
    const paramType = type || "String";
    if (!validTypes.includes(paramType)) {
      return res.status(400).json({
        success: false,
        error: "ValidationError",
        message: `Type must be one of: ${validTypes.join(", ")}`,
      });
    }

    const result = await setParameter(
      name,
      value,
      paramType,
      description || "",
      overwrite !== false
    );

    res.json(result);
  } catch (error) {
    console.error(`Error setting parameter:`, error.message);
    
    let statusCode = 500;
    let errorMessage = error.message;

    // Map AWS errors to appropriate HTTP status codes
    switch (error.name) {
      case "ParameterAlreadyExists":
        statusCode = 409;
        errorMessage = "Parameter already exists. Set 'overwrite: true' to update it.";
        break;
      case "AccessDeniedException":
        statusCode = 403;
        errorMessage = "Access denied. Check your IAM permissions for ssm:PutParameter.";
        break;
      case "InvalidParameterValueException":
        statusCode = 400;
        errorMessage = "Invalid parameter value provided.";
        break;
    }

    res.status(statusCode).json({
      success: false,
      error: error.name || "UnknownError",
      message: errorMessage,
    });
  }
});

/**
 * GET /health - Health check endpoint
 */
app.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "SSM Parameter Store API is running",
    region: region,
    pathPrefix: PATH_PREFIX,
    timestamp: new Date().toISOString(),
  });
});

// Start Server
app.listen(PORT, () => {
  console.log("🚀 SSM Parameter Store API");
  console.log(`┌─ Running on: http://localhost:${PORT}`);
  console.log(`├─ Region: ${region}`);
  console.log(`└─ Path Prefix: ${PATH_PREFIX}`);
  console.log("\n📋 Available Endpoints:");
  console.log("┌─ GET  /health          - Health check");
  console.log("├─ GET  /parameters      - Retrieve parameters");
  console.log("└─ PUT  /parameters      - Set/update parameters");
  console.log("\n🔗 Quick Examples:");
  console.log(`curl http://localhost:${PORT}/health`);
  console.log(`curl http://localhost:${PORT}/parameters`);
  console.log(`curl -X PUT http://localhost:${PORT}/parameters -H "Content-Type: application/json" -d '{"name":"/test","value":"hello"}'`);
});
