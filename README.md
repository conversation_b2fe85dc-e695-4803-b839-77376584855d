# SSM Parameter Store API

A Node.js Express API for managing AWS Systems Manager (SSM) parameters with both read and write capabilities.

## ✨ Features

- ✅ **AWS SDK v3** - Latest AWS SDK with modern async/await support
- 🔒 **Secure** - Masks SecureString parameter values in responses
- 📊 **Multiple Formats** - JSON and Markdown output formats
- 🎯 **RESTful API** - Clean REST endpoints for parameter management
- 🛡️ **Error Handling** - Comprehensive error handling with proper HTTP status codes
- 📝 **Parameter Management** - Create, update, and retrieve parameters
- 🔍 **Path Filtering** - Configurable path prefixes for parameter organization

## 📋 Prerequisites

- Node.js (v14 or higher)
- AWS account with appropriate IAM permissions
- AWS credentials configured

## 🚀 Quick Start

1. **<PERSON>lone and Install**
   ```bash
   git clone <repository-url>
   cd parameter-store-data
   pnpm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your AWS credentials
   ```

3. **Start the Server**
   ```bash
   pnpm start
   ```

4. **Test the API**
   ```bash
   curl http://localhost:3000/health
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# AWS Configuration
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# API Configuration
SSM_PATH_PREFIX=/
PORT=3000
```

| Variable | Description | Default |
|----------|-------------|---------|
| `AWS_REGION` | AWS region to use | `eu-west-1` |
| `SSM_PATH_PREFIX` | Parameter path prefix to filter | `/` (all parameters) |
| `AWS_ACCESS_KEY_ID` | AWS access key | Required |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | Required |
| `PORT` | Server port | `3000` |

### AWS Credentials Setup

Choose one of these methods:

#### Option A: Environment Variables (.env file)
Add your credentials to the `.env` file (recommended for development).

#### Option B: AWS CLI
```bash
aws configure
```

#### Option C: IAM Roles
Use IAM roles when running on AWS (EC2, Lambda, etc.) - no additional configuration needed.

### Required IAM Permissions

Your AWS user/role needs these permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ssm:GetParametersByPath",
        "ssm:GetParameters",
        "ssm:PutParameter"
      ],
      "Resource": "arn:aws:ssm:REGION:ACCOUNT:parameter/PATH_PREFIX*"
    }
  ]
}
```

Replace `REGION`, `ACCOUNT`, and `PATH_PREFIX` with your specific values.

## 🔌 API Endpoints

### Health Check
**GET** `/health`

Returns the health status of the API.

```bash
curl http://localhost:3000/health
```

**Response:**
```json
{
  "success": true,
  "message": "SSM Parameter Store API is running",
  "region": "eu-west-1",
  "pathPrefix": "/",
  "timestamp": "2025-06-15T09:30:00.000Z"
}
```

### Get Parameters
**GET** `/parameters`

Retrieves parameters from the configured path prefix.

**Query Parameters:**
- `format` (optional): Response format (`json` or `markdown`)

**Examples:**
```bash
# Get parameters as JSON
curl http://localhost:3000/parameters

# Get parameters as Markdown report
curl http://localhost:3000/parameters?format=markdown
```

**JSON Response:**
```json
{
  "success": true,
  "region": "eu-west-1",
  "pathPrefix": "/",
  "totalParameters": 3,
  "parameters": [
    {
      "name": "/app/config/database_url",
      "type": "SecureString",
      "value": "[SECURE VALUE]",
      "version": 2,
      "lastModifiedDate": "2025-06-15T09:30:00.000Z",
      "arn": "arn:aws:ssm:eu-west-1:123456789012:parameter/app/config/database_url"
    },
    {
      "name": "/app/config/api_endpoint",
      "type": "String",
      "value": "https://api.example.com",
      "version": 1,
      "lastModifiedDate": "2025-06-15T09:25:00.000Z",
      "arn": "arn:aws:ssm:eu-west-1:123456789012:parameter/app/config/api_endpoint"
    }
  ]
}
```

### Set Parameter
**PUT** `/parameters`

Creates or updates a parameter in Parameter Store.

**Request Body:**
```json
{
  "name": "/app/config/database_url",
  "value": "postgresql://user:pass@localhost:5432/mydb",
  "type": "SecureString",
  "description": "Database connection URL",
  "overwrite": true
}
```

**Request Fields:**
- `name` (required): Parameter name/path
- `value` (required): Parameter value
- `type` (optional): Parameter type - `String`, `StringList`, or `SecureString` (default: `String`)
- `description` (optional): Parameter description
- `overwrite` (optional): Whether to overwrite existing parameter (default: `true`)

**Examples:**

```bash
# Set a string parameter
curl -X PUT http://localhost:3000/parameters \
  -H "Content-Type: application/json" \
  -d '{
    "name": "/app/config/api_key",
    "value": "your-api-key-here",
    "type": "String",
    "description": "API key for external service"
  }'

# Set a secure parameter (encrypted)
curl -X PUT http://localhost:3000/parameters \
  -H "Content-Type: application/json" \
  -d '{
    "name": "/app/config/database_password",
    "value": "super-secret-password",
    "type": "SecureString",
    "description": "Database password"
  }'

# Set a string list parameter
curl -X PUT http://localhost:3000/parameters \
  -H "Content-Type: application/json" \
  -d '{
    "name": "/app/config/allowed_origins",
    "value": "https://app.example.com,https://api.example.com",
    "type": "StringList",
    "description": "Allowed CORS origins"
  }'
```

**Success Response:**
```json
{
  "success": true,
  "name": "/app/config/database_url",
  "version": 1,
  "message": "Parameter /app/config/database_url set successfully"
}
```

## 🗂️ Parameter Organization

### Path Prefix Examples

Configure `SSM_PATH_PREFIX` to organize parameters:

- `/` - All parameters (default)
- `/app/` - Application-specific parameters
- `/prod/database/` - Production database parameters
- `/dev/` - Development environment parameters
- `/shared/` - Shared configuration across environments

### Naming Conventions

Follow these best practices for parameter names:

```
/environment/service/component/setting
```

Examples:
- `/prod/api/database/connection_string`
- `/dev/frontend/auth/jwt_secret`
- `/shared/monitoring/slack_webhook`

## 📊 Output Formats

### JSON Format (Default)
Structured data perfect for programmatic access and integration with other tools.

### Markdown Format
Human-readable reports that are automatically saved to the `ssm-data/` directory:

1. **Timestamped file**: `ssm-parameters-YYYY-MM-DDTHH-MM-SS-sssZ.md`
2. **Latest file**: `ssm-parameters-latest.md`

**Sample Markdown Output:**
```markdown
# SSM Parameters Report

**Generated:** 6/15/2025, 10:27:38 AM  
**Region:** eu-west-1  
**Path Prefix:** /  
**Total Parameters:** 3

## Parameters

| Parameter Name | Type | Value |
|---------------|------|-------|
| /app/database/host | String | db.example.com |
| /app/database/password | SecureString | [SECURE VALUE] |
| /app/api/key | SecureString | [SECURE VALUE] |
```

## 🛠️ Integration Examples

### JavaScript/Node.js

```javascript
// Get parameters
const response = await fetch('http://localhost:3000/parameters');
const data = await response.json();
console.log(`Found ${data.totalParameters} parameters`);

// Set a parameter
const setResponse = await fetch('http://localhost:3000/parameters', {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '/app/config/feature_flag',
    value: 'true',
    type: 'String',
    description: 'Enable new feature'
  })
});
const result = await setResponse.json();
console.log(result.message);
```

### Python

```python
import requests

# Get parameters
response = requests.get('http://localhost:3000/parameters')
data = response.json()
print(f"Found {data['totalParameters']} parameters")

# Set a parameter
payload = {
    'name': '/app/config/debug_mode',
    'value': 'false',
    'type': 'String',
    'description': 'Debug mode flag'
}
response = requests.put('http://localhost:3000/parameters', json=payload)
result = response.json()
print(result['message'])
```

### Shell Script

```bash
#!/bin/bash

API_BASE="http://localhost:3000"

# Function to set parameter
set_parameter() {
    local name="$1"
    local value="$2"
    local type="${3:-String}"
    local description="$4"
    
    curl -X PUT "$API_BASE/parameters" \
        -H "Content-Type: application/json" \
        -d "{\"name\":\"$name\",\"value\":\"$value\",\"type\":\"$type\",\"description\":\"$description\"}"
}

# Set multiple parameters
set_parameter "/app/version" "1.2.3" "String" "Application version"
set_parameter "/app/debug" "false" "String" "Debug mode"
set_parameter "/app/secret" "mysecret123" "SecureString" "Application secret"
```

## ❌ Error Handling

The API returns appropriate HTTP status codes and detailed error messages:

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| `400` | Bad Request | Invalid request parameters or validation errors |
| `403` | Forbidden | Insufficient AWS permissions |
| `409` | Conflict | Parameter already exists (when overwrite is false) |
| `500` | Internal Server Error | AWS or system errors |

**Example Error Response:**
```json
{
  "success": false,
  "error": "ValidationError",
  "message": "Both 'name' and 'value' are required"
}
```

**Common Error Scenarios:**

1. **Missing IAM Permissions**
   ```json
   {
     "success": false,
     "error": "AccessDeniedException",
     "message": "Access denied. Check your IAM permissions for ssm:PutParameter."
   }
   ```

2. **Parameter Already Exists**
   ```json
   {
     "success": false,
     "error": "ParameterAlreadyExists",
     "message": "Parameter already exists. Set 'overwrite: true' to update it."
   }
   ```

3. **Invalid Parameter Type**
   ```json
   {
     "success": false,
     "error": "ValidationError",
     "message": "Type must be one of: String, StringList, SecureString"
   }
   ```

## 🔧 Troubleshooting

### Permission Errors
If you see `AccessDeniedException`:
1. Verify your IAM user has the required permissions
2. Check the resource ARN matches your parameter paths
3. Ensure you're in the correct AWS region

### Credential Errors
If you see credential-related errors:
1. Verify your `.env` file has correct credentials
2. Test AWS CLI: `aws sts get-caller-identity`
3. Check if you're using the correct AWS profile

### Connection Errors
If the API won't start:
1. Check if port 3000 is available: `lsof -i :3000`
2. Verify all dependencies are installed: `pnpm install`
3. Check Node.js version: `node --version` (requires v14+)

### No Parameters Found
If no parameters are returned:
1. Verify the `SSM_PATH_PREFIX` is correct
2. Check if parameters exist in the specified region
3. Ensure you're connected to the correct AWS account

## 🔐 Security Notes

- **SecureString Values**: Automatically masked in all GET responses
- **Environment Variables**: Never commit `.env` files with real credentials
- **IAM Best Practices**: Use IAM roles instead of access keys when possible
- **Network Security**: Consider using VPC endpoints for production deployments
- **Parameter Encryption**: Use SecureString type for sensitive data
- **Access Logging**: Consider enabling CloudTrail for audit logs

## 📝 Development

### Running in Development

```bash
# Install dependencies
pnpm install

# Start with auto-reload (if using nodemon)
pnpm run dev

# Or start normally
pnpm start
```

### Code Structure

```
├── index.js              # Main application file
├── package.json          # Dependencies and scripts
├── .env.example          # Environment template
├── README.md            # This documentation
└── ssm-data/           # Generated markdown reports
    ├── ssm-parameters-latest.md
    └── ssm-parameters-*.md
```

### Testing the API

```bash
# Health check
curl http://localhost:3000/health

# Get all parameters
curl http://localhost:3000/parameters

# Get parameters as markdown
curl http://localhost:3000/parameters?format=markdown

# Set a test parameter
curl -X PUT http://localhost:3000/parameters \
  -H "Content-Type: application/json" \
  -d '{"name":"/test/hello","value":"world","description":"Test parameter"}'
```

## 📄 License

MIT License - see LICENSE file for details.

---

**Need help?** Check the troubleshooting section above or review the AWS SSM documentation.
