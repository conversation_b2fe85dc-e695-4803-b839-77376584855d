
---
**Prompt for Copilot: Build a Robust AWS Parameter Store Module for NestJS**
**Objective:**
Create a production-ready NestJS module that dynamically loads environment variables from AWS Parameter Store when `NODE_ENV=production`, while falling back to `.env` files in development. The solution should use AWS SDK v3 and include proper error handling, caching, and retry mechanisms.
**Requirements:**
1. **Environment Detection:**
   - Load from Parameter Store only when `NODE_ENV=production`
   - Fall back to `.env` file when in development (`start:dev`)
   - Handle edge case where `npm run start:dev` is used with `NODE_ENV=production`
2. **AWS Parameter Store Integration:**
   - Use AWS SDK v3 (`@aws-sdk/client-ssm`)
   - Support secure string parameters
   - Implement hierarchical parameter loading using the configured prefix
3. **Configuration:**
   - Required minimum environment variables:
     ```env
     NODE_ENV=production
     PORT=8000

     # AWS Configuration
     AWS_REGION=eu-west-1
     AWS_ACCESS_KEY_ID=********************
     AWS_SECRET_ACCESS_KEY=SDbD80ro5YJAobpruD/4GDBiX7NDi5qVmHgC2obn

     # AWS Parameter Store Configuration
     AWS_PARAMETER_STORE_PREFIX=/reach/prod/api
     AWS_PARAMETER_STORE_CACHE_TTL=300
     AWS_PARAMETER_STORE_RETRY_ATTEMPTS=3
     AWS_PARAMETER_STORE_RETRY_DELAY=1000
     AWS_PARAMETER_STORE_ENABLED=true
     ```
4. **Features:**
   - Parameter caching with configurable TTL
   - Retry mechanism with configurable attempts/delay
   - Health check endpoint to verify Parameter Store connectivity
   - Graceful fallback if Parameter Store is unavailable
   - Type-safe configuration using NestJS ConfigService
5. **Implementation Guidelines:**
   - Create a dynamic module `ParameterStoreModule`
   - Use decorators for optional parameter injection
   - Implement proper logging for debugging
   - Include unit and integration tests
   - Document usage examples
6. **Production Considerations:**
   - Secure credential handling
   - Performance optimization
   - Thread-safe implementation
   - Proper error states and recovery
**Deliverables:**
1. Complete NestJS module implementation
2. Example usage in a sample application
3. Unit tests with 80%+ coverage
4. Documentation including:
   - Installation instructions
   - Configuration options
   - Common troubleshooting
**Additional Notes:**
- Prioritize security best practices
- Include proper type definitions
- Implement cache invalidation strategy
- Consider adding metrics for monitoring
---
This version:
1. Structures the requirements clearly
2. Separates concerns logically
3. Provides specific technical guidance
4. Includes all necessary details without being verbose
5. Sets clear expectations for deliverables
6. Maintains security considerations


This is the variables I pulled from the store
| Parameter Name                                  | Type         | Value                             |
| ----------------------------------------------- | ------------ | --------------------------------- |
| /reach/prod/api/ACCESS_TOKEN_EXPIRY             | String       | 7d                                |
| /reach/prod/api/AWS_CLOUDFRONT_URL              | String       | https://d26he86s03m16r.cloudfr... |
| /reach/prod/api/CACHE_ENABLED                   | String       | true                              |
| /reach/prod/api/EMAIL_TOGGLE                    | String       | ON                                |
| /reach/prod/api/MAGIC_LINK_EXPIRY               | String       | 10m                               |
| /reach/prod/api/MAILTRAP_HOST                   | String       | smtp-mail.outlook.com             |
| /reach/prod/api/NODE_ENV                        | String       | production                        |
| /reach/prod/api/QUEUE_ATTEMPTS                  | String       | 3                                 |
| /reach/prod/api/REDIS_DB                        | String       | 0                                 |
| /reach/prod/api/SENTRY_DSN                      | String       | https://sentry.io/organization... |
| /reach/prod/api/AWS_ENDPOINT                    | String       | https://touching-lives-media-a... |
| /reach/prod/api/DATABASE_URL                    | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/FRONTEND_URL                    | String       | https://reach.amalitech.com       |
| /reach/prod/api/MAILTRAP_USER                   | String       | <EMAIL>      |
| /reach/prod/api/PORT                            | String       | 3003                              |
| /reach/prod/api/REFRESH_TOKEN_SECRET            | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/SENDER_EMAIL                    | String       | <EMAIL>      |
| /reach/prod/api/SMTP_PASSWORD                   | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/SMTP_PORT                       | String       | 465                               |
| /reach/prod/api/SUPER_ADMIN_STATE               | String       | active                            |
| /reach/prod/api/API_DOC_PASSWORD                | StringList   | hjljkhkjh                         |
| /reach/prod/api/API_DOC_USERNAME                | String       | touchingstudentlives              |
| /reach/prod/api/AWS_ACCESS_KEY_ID               | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/CACHE_WARMUP_ENABLED            | String       | true                              |
| /reach/prod/api/EMAIL_ENVIRONMENT               | String       | production                        |
| /reach/prod/api/MAIL_EMAIL                      | String       | <EMAIL>      |
| /reach/prod/api/QUEUE_REMOVE_ON_FAIL            | String       | false                             |
| /reach/prod/api/REDIS_HOST                      | String       | ************                      |
| /reach/prod/api/SMTP_USERNAME                   | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/THROTTLE_TTL                    | String       | 6000                              |
| /reach/prod/api/APP_NAME                        | String       | Reach                             |
| /reach/prod/api/BACKEND_URL                     | String       | https://api.reach.amalitech.co... |
| /reach/prod/api/BULL_BOARD_PASSWORD             | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/CACHE_NAMESPACE                 | String       | app                               |
| /reach/prod/api/MAILTRAP_PASS                   | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/MAILTRAP_PORT                   | String       | 587                               |
| /reach/prod/api/MAX_FILE_SIZE                   | String       | 2048                              |
| /reach/prod/api/QUEUE_BACKOFF_DELAY             | String       | 1000                              |
| /reach/prod/api/SMTP_ENDPOINT                   | String       | email-smtp.eu-west-1.amazonaws... |
| /reach/prod/api/SUPER_ADMIN_ROLE                | String       | super_admin                       |
| /reach/prod/api/AWS_BUCKET_NAME                 | String       | touching-lives-media-assets       |
| /reach/prod/api/AWS_REGION                      | String       | eu-west-1                         |
| /reach/prod/api/EMAIL_PORT                      | String       | 587                               |
| /reach/prod/api/EMAIL_TOKEN                     | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/OTP_EXPIRY_TIME                 | String       | 600                               |
| /reach/prod/api/REDIS_CLUSTER_NODES             | String       | ""                                |
| /reach/prod/api/REDIS_PORT                      | String       | 6380                              |
| /reach/prod/api/REDIS_TLS                       | String       | true                              |
| /reach/prod/api/REFRESH_TOKEN_EXPIRY            | String       | 30d                               |
| /reach/prod/api/SUPER_ADMIN_EMAIL               | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/ACCESS_TOKEN_SECRET             | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/AWS_SECRET_ACCESS_KEY           | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/BULL_BOARD_USERNAME             | String       | admin                             |
| /reach/prod/api/CACHE_DEFAULT_TTL               | String       | 3600                              |
| /reach/prod/api/CACHE_VERSION                   | String       | 1                                 |
| /reach/prod/api/EMAIL_HOST                      | String       | smtp-mail.outlook.com             |
| /reach/prod/api/FIREBASE_SERVICE_ACCOUNT_BASE64 | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/PASSWORD                        | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/REDIS_CLUSTER_ENABLED           | String       | false                             |
| /reach/prod/api/THROTTLE_LIMIT                  | String       | 100                               |
| /reach/prod/api/OTP_HASH_SECRET                 | SecureString | [SECURE VALUE]                    |
| /reach/prod/api/QUEUE_CONCURRENCY               | String       | 5                                 |
| /reach/prod/api/QUEUE_REMOVE_ON_COMPLETE        | String       | true                              |

