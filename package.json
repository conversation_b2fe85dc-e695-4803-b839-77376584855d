{"name": "ssm-parameter-store-api", "version": "1.0.0", "description": "RESTful API for managing AWS SSM Parameter Store parameters", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node index.js", "test": "node -c index.js && echo '✅ Syntax check passed'"}, "keywords": ["aws", "ssm", "parameter-store", "api", "express", "nodejs"], "dependencies": {"@aws-sdk/client-ssm": "^3.826.0", "dotenv": "^16.0.0", "express": "^4.18.2"}}